<?php
require_once 'includes/db.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $sender_name  = $_POST['sender_name'];
    $sender_email = $_POST['sender_email'];
    $subject      = $_POST['subject'];
    $message      = $_POST['message'];
    $attachment   = '';
    $maxFileSize  = 2 * 1024 * 1024; // 2MB
    $allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png'];

    // Handle file upload with validation
    if (isset($_FILES['attachment']) && $_FILES['attachment']['error'] === 0) {
        $fileType = $_FILES['attachment']['type'];
        $fileSize = $_FILES['attachment']['size'];

        if (!in_array($fileType, $allowedTypes)) {
            die("<p style='color:red;'>❌ Invalid file type. Only PDF, DOCX, JPG, PNG allowed.</p>");
        }

        if ($fileSize > $maxFileSize) {
            die("<p style='color:red;'>❌ File too large. Maximum allowed size is 2MB.</p>");
        }

        $upload_dir = "assets/uploads/";
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        $original_name = basename($_FILES['attachment']['name']);
        $new_filename  = time() . "_" . $original_name;
        $target_path   = $upload_dir . $new_filename;

        if (move_uploaded_file($_FILES["attachment"]["tmp_name"], $target_path)) {
            $attachment = $target_path;
        } else {
            die("<p style='color:red;'>❌ File upload failed.</p>");
        }
    }

    // Save to database
    $stmt = $pdo->prepare("INSERT INTO mails (sender_name, sender_email, subject, message, attachment, status) VALUES (?, ?, ?, ?, ?, 'pending')");
    $stmt->execute([$sender_name, $sender_email, $subject, $message, $attachment]);
    

    // Get last inserted mail ID
    $mail_id = $pdo->lastInsertId();

    // Redirect to success page with reference number
    header("Location: success.php?ref=" . $mail_id);
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Submit Mail</title>
    <link rel="stylesheet" href="assets/style.css">
</head>

<body>
    <div class="container">
        <div class="form-card">
            <h2>📬 Submit a Mail to the Organization</h2>
            <form action="submit-mail.php" method="post" enctype="multipart/form-data">
                <label>Your Name:</label>
                <input type="text" name="sender_name" required>

                <label>Your Email (optional):</label>
                <input type="email" name="sender_email">

                <label>Subject:</label>
                <input type="text" name="subject" required>

                <label>Message:</label>
                <textarea name="message" rows="6" required></textarea>

                <label>Attachment (optional):</label>
                <input type="file" name="attachment">

                <button type="submit">📤 Submit Mail</button>
            </form>
        </div>
    </div>
</body>

</html>