<?php
require 'includes/db.php';

// Fetch summarized mails
$stmt = $pdo->prepare("SELECT id, sender_name, subject, summary, purpose FROM mails WHERE status = 'summarized'");
$stmt->execute();
$mails = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html>
<head>
    <title>Leader Dashboard</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f4f6f9;
        }
        h2 {
            color: #333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        th {
            background-color: #0066cc;
            color: #fff;
        }
        select {
            padding: 6px;
            border-radius: 5px;
            border: 1px solid #ccc;
            width: 100%;
        }
        button {
            margin-top: 15px;
            padding: 10px 20px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 6px;
            font-weight: bold;
            cursor: pointer;
        }
        button:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <h2>📬 Leader Dashboard – Review Summarized Mails</h2>

    <?php if (count($mails) === 0): ?>
        <p>No summarized mails available.</p>
    <?php else: ?>
        <form action="process-leader-actions.php" method="POST">
            <table>
                <thead>
                    <tr>
                        <th>Sender</th>
                        <th>Subject</th>
                        <th>Summary</th>
                        <th>Purpose</th>
                        <th>Leader Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($mails as $mail): ?>
                        <tr>
                            <td><?= htmlspecialchars($mail['sender_name']) ?></td>
                            <td><?= htmlspecialchars($mail['subject']) ?></td>
                            <td><?= nl2br(htmlspecialchars($mail['summary'])) ?></td>
                            <td><?= htmlspecialchars($mail['purpose']) ?></td>
                            <td>
                                <select name="actions[<?= $mail['id'] ?>]" required>
                                    <option value="">-- Select Action --</option>
                                    <option value="Approved">✅ Approve</option>
                                    <option value="Rejected">❌ Reject</option>
                                    <option value="Follow Up">🔄 Follow Up</option>
                                </select>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <button type="submit">📤 Submit All Decisions</button>
        </form>
    <?php endif; ?>
</body>
</html>
