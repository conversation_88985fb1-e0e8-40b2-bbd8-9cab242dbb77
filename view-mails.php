<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);


// Include database connection
require 'includes/db.php';

try {
    // Check if mails table exists
    $tableCheck = $pdo->query("SELECT COUNT(*) FROM information_schema.tables 
                              WHERE table_schema = 'mail_system' 
                              AND table_name = 'mails'");

    if ($tableCheck->fetchColumn() == 0) {
        throw new Exception("'mails' table doesn't exist in the database");
    }

    // Fetch all mails
    $stmt = $pdo->query("SELECT * FROM mails ORDER BY submitted_at DESC");
    $mails = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Database Error: " . $e->getMessage();
} catch (Exception $e) {
    $error = "System Error: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html>

<head>
    <title>Incoming Mails</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        .table-container {
            margin: 40px auto;
            max-width: 90%;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        h2 {
            text-align: center;
            color: #6a0dad;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th,
        td {
            padding: 12px;
            border: 1px solid #ccc;
            text-align: left;
        }

        th {
            background-color: #0b5ed7;
            color: white;
        }

        tr:nth-child(even) {
            background-color: #f8f8f8;
        }

        .action-btn {
            background-color: #6a0dad;
            color: white;
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
        }

        .action-btn:hover {
            background-color: #0b5ed7;
        }

        .error-message {
            color: #d32f2f;
            background: #ffebee;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
            border-left: 4px solid #d32f2f;
            font-family: monospace;
        }
    </style>
</head>

<body>
    <div class="table-container">
        <h2>📥 All Submitted Mails</h2>

        <div style="text-align:center; margin-top:10px;">
            <a href="submit-mail.php" class="action-btn">+ Submit New Mail</a>
        </div>

        <?php if (isset($error)): ?>
            <div class="error-message">
                <h3>🚨 Error Loading Mails</h3>
                <p><?= htmlspecialchars($error) ?></p>
                <p>Please check your database configuration and ensure the 'mails' table exists.</p>
            </div>

        <?php elseif (empty($mails)): ?>
            <div class="error-message">
                <h3>ℹ️ No Mails Found</h3>
                <p>No mails have been submitted yet. The system is ready to receive mails.</p>
            </div>

        <?php else: ?>
            <table>
                <tr>
                    <th>ID</th>
                    <th>Sender</th>
                    <th>Subject</th>
                    <th>Status</th>
                    <th>Date</th>
                    <th>Action</th>
                    <th>summary</th>
                </tr>
                <?php foreach ($mails as $row): ?>
                    <tr>
                        <td><?= $row['id'] ?></td>
                        <td><?= htmlspecialchars($row['sender_name'] ?? 'N/A') ?></td>
                        <td><?= htmlspecialchars($row['subject'] ?? 'No subject') ?></td>
                        <td><?= htmlspecialchars(mb_strimwidth($row['summary'] ?? '—', 0, 30, '...')) ?></td>
                        <td><?= $row['submitted_at'] ?></td>
                        <td><?= ucfirst($row['status'] ?? 'pending') ?></td>
                        <td><?= ucfirst($row['status'] ?? 'Pending') ?></td>
                        <td>
                            <a class="action-btn" href="view-mail-details.php?id=<?= $row['id'] ?>">View</a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </table>
        <?php endif; ?>
    </div>
</body>

</html>