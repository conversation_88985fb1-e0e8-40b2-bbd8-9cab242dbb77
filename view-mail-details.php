<?php
// Enable error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

require 'includes/db.php';

// Validate ID
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    die("<h2>No mail ID provided.</h2>");
}

$mail_id = (int) $_GET['id'];

try {
    $stmt = $pdo->prepare("SELECT * FROM mails WHERE id = ?");
    $stmt->execute([$mail_id]);
    $mail = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$mail) {
        die("<h2>Mail not found.</h2>");
    }
} catch (PDOException $e) {
    die("Database error: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Mail Details</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        .details-container {
            max-width: 800px;
            margin: 40px auto;
            background: #fff;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 6px 20px rgba(0,0,0,0.1);
        }

        h2 {
            color: #6a0dad;
            text-align: center;
        }

        .mail-field {
            margin-bottom: 15px;
        }

        .mail-field label {
            font-weight: bold;
            color: #333;
        }

        .mail-field p {
            background: #f4f4f4;
            padding: 8px;
            border-radius: 6px;
        }

        form {
            margin-top: 30px;
        }

        textarea {
            width: 100%;
            padding: 10px;
            min-height: 100px;
            border: 1px solid #ccc;
            border-radius: 6px;
        }

        select, button {
            padding: 10px;
            margin-top: 10px;
            border-radius: 6px;
            font-size: 16px;
        }

        .btn-submit {
            background: #6a0dad;
            color: white;
            border: none;
            cursor: pointer;
        }

        .btn-submit:hover {
            background: #0b5ed7;
        }
    </style>
</head>
<body>
    <div class="details-container">
        <h2>📄 Mail Details</h2>

        <div class="mail-field">
            <label>Sender Name:</label>
            <p><?= htmlspecialchars($mail['sender_name']) ?></p>
        </div>

        <div class="mail-field">
            <label>Subject:</label>
            <p><?= htmlspecialchars($mail['subject']) ?></p>
        </div>

        <div class="mail-field">
            <label>Message:</label>
            <p><?= nl2br(htmlspecialchars($mail['message'])) ?></p>
        </div>

        <div class="mail-field">
            <label>Submitted At:</label>
            <p><?= $mail['submitted_at'] ?></p>
        </div>

        <!-- Summary form -->
<form action="process-summary.php" method="POST">
    <input type="hidden" name="mail_id" value="<?= $mail_id ?>">

    <label>Summary:</label>
    <textarea name="summary" required placeholder="Write summary..."></textarea>

    <label>Send to Leader as:</label>
    <select name="purpose" required>
        <option value="">-- Select Action --</option>
        <option value="Action">For Action</option>
        <option value="Information">For Information</option>
        <option value="Record">For Record</option>
    </select>

    <button type="submit" class="btn-submit">Send Summary</button>
</form>
    </div>
</body>
</html>
