body {
    margin: 0;
    font-family: 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, #0b5ed7, #6a0dad);
    color: #333;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.container {
    width: 100%;
    max-width: 600px;
    padding: 20px;
}

.form-card {
    background: white;
    border-radius: 15px;
    padding: 30px 40px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    animation: slide-in 0.5s ease-in-out;
}

h2 {
    text-align: center;
    color: #6a0dad;
    margin-bottom: 25px;
}

label {
    display: block;
    margin-top: 15px;
    font-weight: bold;
    color: #333;
}

input[type="text"],
input[type="email"],
textarea,
input[type="file"] {
    width: 100%;
    padding: 10px;
    margin-top: 5px;
    border: 1px solid #ccc;
    border-radius: 8px;
    transition: border-color 0.3s;
}

input:focus,
textarea:focus {
    border-color: #6a0dad;
    outline: none;
}

button {
    margin-top: 20px;
    width: 100%;
    background-color: #0b5ed7;
    color: white;
    font-size: 16px;
    padding: 12px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #6a0dad;
}

@keyframes slide-in {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}
