/* Custom styles to complement Bootstrap */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0b5ed7, #6a0dad);
    min-height: 100vh;
    padding-top: 0;
}

/* Custom card animations */
.card {
    animation: slide-in 0.5s ease-in-out;
    border: none;
}

.card-header {
    border-bottom: none;
}

/* Custom focus styles for form elements */
.form-control:focus,
.form-select:focus {
    border-color: #6a0dad;
    box-shadow: 0 0 0 0.2rem rgba(106, 13, 173, 0.25);
}

/* Custom button hover effects */
.btn-primary:hover {
    background-color: #6a0dad;
    border-color: #6a0dad;
}

.btn-success:hover {
    background-color: #198754;
    border-color: #198754;
}

/* Custom navbar styling */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Custom table styling */
.table-hover tbody tr:hover {
    background-color: rgba(106, 13, 173, 0.1);
}

/* Custom alert styling */
.alert {
    border: none;
    border-radius: 10px;
}

/* Animation for cards */
@keyframes slide-in {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Custom spacing for main content */
.container {
    margin-bottom: 2rem;
}

/* Custom badge styling */
.badge {
    font-size: 0.8em;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }

    .card {
        margin: 0.5rem;
    }
}
